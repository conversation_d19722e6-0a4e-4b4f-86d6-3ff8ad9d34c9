--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

local RUI = LibStub('AceAddon-3.0'):NewAddon('RetailUI', 'AceConsole-3.0')
local AceConfig = LibStub("AceConfig-3.0")
local AceDB = LibStub("AceDB-3.0")

-- Initialize saved variables properly (will be managed by AceDB)
_G.RetailUIDB = _G.RetailUIDB or {}
if _G.RetailUIDB.bagsExpanded == nil then
    _G.RetailUIDB.bagsExpanded = false
end

RUI.InterfaceVersion = select(4, GetBuildInfo())
RUI.Wrath = (RUI.InterfaceVersion >= 30300)
RUI.DB = nil

-- WotLK 3.3.5 Compatibility Layer
RUI.Compat = {
    -- Timer compatibility for C_Timer.After
    After = function(delay, callback)
        if C_Timer and C_Timer.After then
            return C_Timer.After(delay, callback)
        else
            -- WotLK fallback using frame-based timer
            local frame = CreateFrame("Frame")
            local elapsed = 0
            frame:SetScript("OnUpdate", function(self, dt)
                elapsed = elapsed + dt
                if elapsed >= delay then
                    frame:SetScript("OnUpdate", nil)
                    callback()
                end
            end)
            return frame
        end
    end,

    -- API version checks
    IsRetail = function() return RUI.InterfaceVersion >= 90000 end,
    IsWotLK = function() return RUI.InterfaceVersion >= 30300 and RUI.InterfaceVersion < 40000 end,

    -- Safe UnitGUID wrapper for WotLK compatibility
    UnitGUID = function(unit)
        if UnitGUID then
            return UnitGUID(unit)
        else
            -- Fallback for older clients - use unit name as identifier
            return UnitName(unit) or "unknown"
        end
    end,
}

-- Enhanced logging system with levels
RUI.Logger = {
    levels = {ERROR = 1, WARN = 2, INFO = 3, DEBUG = 4},
    currentLevel = 2, -- Default to WARN

    Log = function(self, level, message, ...)
        if self.levels[level] <= self.currentLevel then
            local prefix = "|cFFFF0000[RetailUI-" .. level .. "]|r"
            DEFAULT_CHAT_FRAME:AddMessage(prefix .. " " .. string.format(message, ...))
        end
    end,

    Error = function(self, msg, ...) self:Log("ERROR", msg, ...) end,
    Warn = function(self, msg, ...) self:Log("WARN", msg, ...) end,
    Info = function(self, msg, ...) self:Log("INFO", msg, ...) end,
    Debug = function(self, msg, ...) self:Log("DEBUG", msg, ...) end,
}

-- Input validation utilities
RUI.Validator = {
    ValidateScale = function(self, scale)
        local num = tonumber(scale)
        if not num then return false, "Scale must be a number" end
        if num < RUI.Constants.VALIDATION.MIN_SCALE then return false, "Scale too small (min " .. RUI.Constants.VALIDATION.MIN_SCALE .. ")" end
        if num > RUI.Constants.VALIDATION.MAX_SCALE then return false, "Scale too large (max " .. RUI.Constants.VALIDATION.MAX_SCALE .. ")" end
        return true, num
    end,

    ValidateFrameName = function(self, name)
        if not name or name == "" then return false, "Frame name required" end
        if string.len(name) > RUI.Constants.VALIDATION.MAX_FRAME_NAME_LENGTH then return false, "Frame name too long" end
        if not string.match(name, RUI.Constants.VALIDATION.FRAME_NAME_PATTERN) then return false, "Invalid characters in frame name" end
        return true, name
    end,

    ValidateFrame = function(self, frame, frameName)
        if not frame then return false, "Frame is nil" end
        if not frame.SetPoint then return false, "Invalid frame object" end
        return true, frame
    end,
}

-- Safe module and frame operations
RUI.SafeOps = {
    GetModuleSafely = function(self, moduleName)
        local success, module = pcall(RUI.GetModule, RUI, moduleName)
        if not success then
            RUI.Logger:Error("Module '%s' not found", moduleName)
            return nil
        end
        return module
    end,

    SafeSetPoint = function(self, frame, ...)
        local valid, err = RUI.Validator:ValidateFrame(frame)
        if not valid then
            RUI.Logger:Error("Invalid frame: %s", err)
            return false
        end

        local success, error = pcall(frame.SetPoint, frame, ...)
        if not success then
            RUI.Logger:Error("Error setting frame position: %s", error)
            return false
        end
        return true
    end,
}

-- Event throttling and performance management system
RUI.Throttle = {
    timers = {},
    eventCounts = {},
    lastUpdate = GetTime(),

    -- Throttled function execution
    ThrottledCall = function(self, key, delay, callback, ...)
        local now = GetTime()
        if not self.timers[key] or (now - self.timers[key]) >= delay then
            self.timers[key] = now
            callback(...)
            return true
        end
        return false
    end,

    -- Debounced function execution (only executes after delay with no new calls)
    DebouncedCall = function(self, key, delay, callback, ...)
        local args = {...}

        -- Cancel existing timer
        if self.timers[key] and self.timers[key].frame then
            self.timers[key].frame:SetScript("OnUpdate", nil)
        end

        -- Create new timer
        local frame = CreateFrame("Frame")
        local elapsed = 0
        frame:SetScript("OnUpdate", function(self, dt)
            elapsed = elapsed + dt
            if elapsed >= delay then
                frame:SetScript("OnUpdate", nil)
                callback(unpack(args))
            end
        end)

        self.timers[key] = {frame = frame, time = GetTime()}
    end,

    -- Event frequency monitoring
    TrackEvent = function(self, eventName)
        local now = GetTime()
        if not self.eventCounts[eventName] then
            self.eventCounts[eventName] = {count = 0, lastReset = now}
        end

        local eventData = self.eventCounts[eventName]
        eventData.count = eventData.count + 1

        -- Reset counter every minute
        if now - eventData.lastReset > 60 then
            if eventData.count > 100 then
                RUI.Logger:Warn("High event frequency detected: %s (%d events/min)", eventName, eventData.count)
            end
            eventData.count = 0
            eventData.lastReset = now
        end
    end,

    -- Cleanup old timers
    Cleanup = function(self)
        local now = GetTime()
        for key, timer in pairs(self.timers) do
            if timer.time and (now - timer.time) > 300 then -- 5 minutes
                if timer.frame then
                    timer.frame:SetScript("OnUpdate", nil)
                end
                self.timers[key] = nil
            end
        end
    end,
}

-- Performance monitoring system
RUI.Performance = {
    metrics = {},
    startTimes = {},

    -- Start timing an operation
    StartTimer = function(self, operation)
        self.startTimes[operation] = GetTime()
    end,

    -- End timing and record metric
    EndTimer = function(self, operation)
        if not self.startTimes[operation] then return end

        local duration = GetTime() - self.startTimes[operation]
        self.startTimes[operation] = nil

        if not self.metrics[operation] then
            self.metrics[operation] = {
                totalTime = 0,
                callCount = 0,
                maxTime = 0,
                minTime = math.huge
            }
        end

        local metric = self.metrics[operation]
        metric.totalTime = metric.totalTime + duration
        metric.callCount = metric.callCount + 1
        metric.maxTime = math.max(metric.maxTime, duration)
        metric.minTime = math.min(metric.minTime, duration)

        -- Warn about slow operations
        if duration > 0.1 then -- 100ms
            RUI.Logger:Warn("Slow operation detected: %s took %.3fs", operation, duration)
        end
    end,

    -- Get performance report
    GetReport = function(self)
        local report = {}
        for operation, metric in pairs(self.metrics) do
            report[operation] = {
                avgTime = metric.totalTime / metric.callCount,
                totalTime = metric.totalTime,
                callCount = metric.callCount,
                maxTime = metric.maxTime,
                minTime = metric.minTime
            }
        end
        return report
    end,

    -- Print performance report
    PrintReport = function(self)
        local report = self:GetReport()
        RUI.Logger:Info("=== Performance Report ===")

        -- Sort by total time to show most impactful operations first
        local sortedOps = {}
        for operation, data in pairs(report) do
            table.insert(sortedOps, {operation = operation, data = data})
        end

        table.sort(sortedOps, function(a, b)
            return a.data.totalTime > b.data.totalTime
        end)

        for _, entry in pairs(sortedOps) do
            local operation = entry.operation
            local data = entry.data

            RUI.Logger:Info("%s: avg=%.3fms, calls=%d, max=%.3fms, total=%.3fs",
                operation, data.avgTime * 1000, data.callCount, data.maxTime * 1000, data.totalTime)

            -- Add performance warnings
            if data.avgTime > 0.05 then -- 50ms average
                RUI.Logger:Warn("  ⚠️ High average time for %s", operation)
            end
            if data.maxTime > 0.1 then -- 100ms max
                RUI.Logger:Warn("  ⚠️ High maximum time for %s", operation)
            end
            if data.callCount > 1000 then
                RUI.Logger:Warn("  ⚠️ High call frequency for %s", operation)
            end
        end

        -- Generate optimization recommendations
        self:PrintOptimizationRecommendations(report)
    end,

    -- Print optimization recommendations
    PrintOptimizationRecommendations = function(self, report)
        RUI.Logger:Info("=== Optimization Recommendations ===")

        local recommendations = {}

        for operation, data in pairs(report) do
            if data.avgTime > 0.05 then
                table.insert(recommendations, "Consider optimizing " .. operation .. " (high average time)")
            end

            if data.callCount > 1000 and data.avgTime > 0.01 then
                table.insert(recommendations, "Consider throttling " .. operation .. " (high frequency + time)")
            end

            if data.maxTime > 0.2 then
                table.insert(recommendations, "Investigate " .. operation .. " for blocking operations")
            end
        end

        if #recommendations == 0 then
            RUI.Logger:Info("✅ No performance issues detected")
        else
            for _, rec in pairs(recommendations) do
                RUI.Logger:Info("💡 %s", rec)
            end
        end
    end,

    -- Get system performance metrics
    GetSystemMetrics = function(self)
        local metrics = {}

        -- Memory usage
        metrics.memoryUsage = collectgarbage("count") -- KB

        -- Frame rate (approximate)
        local frameRate = GetFramerate()
        metrics.frameRate = frameRate

        -- Network stats
        local bandwidthIn, bandwidthOut, latency = GetNetStats()
        metrics.latency = latency
        metrics.bandwidthIn = bandwidthIn
        metrics.bandwidthOut = bandwidthOut

        -- Addon memory usage (if available)
        if GetAddOnMemoryUsage then
            metrics.addonMemory = GetAddOnMemoryUsage("RetailUI") or 0
        end

        return metrics
    end,

    -- Print system metrics
    PrintSystemMetrics = function(self)
        local metrics = self:GetSystemMetrics()

        RUI.Logger:Info("=== System Performance ===")
        RUI.Logger:Info("Frame Rate: %.1f FPS", metrics.frameRate)
        RUI.Logger:Info("Memory Usage: %.1f MB", metrics.memoryUsage / 1024)
        RUI.Logger:Info("Latency: %d ms", metrics.latency)

        if metrics.addonMemory then
            RUI.Logger:Info("RetailUI Memory: %.1f KB", metrics.addonMemory)
        end

        -- Performance warnings
        if metrics.frameRate < 30 then
            RUI.Logger:Warn("⚠️ Low frame rate detected")
        end

        if metrics.memoryUsage > 512 * 1024 then -- 512 MB
            RUI.Logger:Warn("⚠️ High memory usage detected")
        end

        if metrics.latency > 300 then
            RUI.Logger:Warn("⚠️ High latency detected")
        end
    end,
}

-- Memory management and frame lifecycle system
RUI.Memory = {
    frames = {},
    modules = {},
    timers = {},
    hooks = {},

    -- Register a frame for lifecycle management
    RegisterFrame = function(self, frame, module, frameType)
        if not frame then return end

        local frameInfo = {
            frame = frame,
            module = module or "unknown",
            frameType = frameType or "generic",
            created = GetTime(),
            lastAccess = GetTime(),
            references = 1
        }

        self.frames[frame] = frameInfo
        RUI.Logger:Debug("Registered frame: %s (%s)", frameInfo.frameType, frameInfo.module)
    end,

    -- Unregister and cleanup a frame
    UnregisterFrame = function(self, frame)
        if not frame or not self.frames[frame] then return end

        local frameInfo = self.frames[frame]

        -- Hide and cleanup the frame
        if frame.Hide then frame:Hide() end
        if frame.SetParent then frame:SetParent(nil) end
        if frame.SetScript then
            frame:SetScript("OnUpdate", nil)
            frame:SetScript("OnEvent", nil)
            frame:SetScript("OnShow", nil)
            frame:SetScript("OnHide", nil)
        end

        -- Clear references
        self.frames[frame] = nil

        RUI.Logger:Debug("Unregistered frame: %s (%s)", frameInfo.frameType, frameInfo.module)
    end,

    -- Register a module for cleanup
    RegisterModule = function(self, module, moduleName)
        self.modules[moduleName] = {
            module = module,
            registered = GetTime(),
            frames = {}
        }
    end,

    -- Cleanup all frames for a module
    CleanupModule = function(self, moduleName)
        if not self.modules[moduleName] then return end

        local moduleInfo = self.modules[moduleName]
        local cleanedCount = 0

        -- Find and cleanup all frames belonging to this module
        for frame, frameInfo in pairs(self.frames) do
            if frameInfo.module == moduleName then
                self:UnregisterFrame(frame)
                cleanedCount = cleanedCount + 1
            end
        end

        -- Cleanup module reference
        self.modules[moduleName] = nil

        RUI.Logger:Info("Cleaned up module %s: %d frames removed", moduleName, cleanedCount)
    end,

    -- Register a timer for cleanup
    RegisterTimer = function(self, timerKey, frame)
        self.timers[timerKey] = {
            frame = frame,
            created = GetTime()
        }
    end,

    -- Cleanup old timers
    CleanupTimers = function(self)
        local now = GetTime()
        local cleanedCount = 0

        for key, timer in pairs(self.timers) do
            if (now - timer.created) > 300 then -- 5 minutes
                if timer.frame and timer.frame.SetScript then
                    timer.frame:SetScript("OnUpdate", nil)
                end
                self.timers[key] = nil
                cleanedCount = cleanedCount + 1
            end
        end

        if cleanedCount > 0 then
            RUI.Logger:Debug("Cleaned up %d old timers", cleanedCount)
        end
    end,

    -- Register a hook for cleanup
    RegisterHook = function(self, hookInfo)
        table.insert(self.hooks, hookInfo)
    end,

    -- Cleanup all hooks
    CleanupHooks = function(self)
        for _, hookInfo in pairs(self.hooks) do
            if hookInfo.object and hookInfo.method and hookInfo.object.Unhook then
                pcall(hookInfo.object.Unhook, hookInfo.object, hookInfo.method)
            end
        end
        self.hooks = {}
        RUI.Logger:Debug("Cleaned up all hooks")
    end,

    -- Get memory usage report
    GetMemoryReport = function(self)
        local report = {
            totalFrames = 0,
            framesByModule = {},
            framesByType = {},
            oldestFrame = nil,
            newestFrame = nil,
            totalTimers = 0,
            totalHooks = #self.hooks
        }

        local now = GetTime()
        local oldestTime = now
        local newestTime = 0

        for frame, frameInfo in pairs(self.frames) do
            report.totalFrames = report.totalFrames + 1

            -- Count by module
            report.framesByModule[frameInfo.module] = (report.framesByModule[frameInfo.module] or 0) + 1

            -- Count by type
            report.framesByType[frameInfo.frameType] = (report.framesByType[frameInfo.frameType] or 0) + 1

            -- Track oldest and newest
            if frameInfo.created < oldestTime then
                oldestTime = frameInfo.created
                report.oldestFrame = frameInfo
            end
            if frameInfo.created > newestTime then
                newestTime = frameInfo.created
                report.newestFrame = frameInfo
            end
        end

        report.totalTimers = 0
        for _ in pairs(self.timers) do
            report.totalTimers = report.totalTimers + 1
        end

        return report
    end,

    -- Print memory usage report
    PrintMemoryReport = function(self)
        local report = self:GetMemoryReport()

        RUI.Logger:Info("=== Memory Usage Report ===")
        RUI.Logger:Info("Total Frames: %d", report.totalFrames)
        RUI.Logger:Info("Total Timers: %d", report.totalTimers)
        RUI.Logger:Info("Total Hooks: %d", report.totalHooks)

        RUI.Logger:Info("Frames by Module:")
        for module, count in pairs(report.framesByModule) do
            RUI.Logger:Info("  %s: %d", module, count)
        end

        RUI.Logger:Info("Frames by Type:")
        for frameType, count in pairs(report.framesByType) do
            RUI.Logger:Info("  %s: %d", frameType, count)
        end
    end,

    -- Perform full cleanup
    FullCleanup = function(self)
        RUI.Logger:Info("Performing full memory cleanup...")

        self:CleanupTimers()
        self:CleanupHooks()
        RUI.Throttle:Cleanup()

        -- Force garbage collection
        collectgarbage("collect")

        RUI.Logger:Info("Memory cleanup completed")
    end,
}

-- Security and permission management system
RUI.Security = {
    permissions = {},
    trustedSources = {},
    securityLevel = 2, -- 1=Low, 2=Medium, 3=High

    -- Initialize security system
    Initialize = function(self)
        -- Set default permissions
        self.permissions = {
            modifyFrames = true,
            saveSettings = true,
            executeCommands = true,
            accessFileSystem = false,
            modifySecureFrames = false,
        }

        -- Add trusted sources
        self.trustedSources = {
            ["RetailUI"] = true,
            ["player"] = true,
        }

        RUI.Logger:Info("Security system initialized (Level: %d)", self.securityLevel)
    end,

    -- Check if source has permission for action
    HasPermission = function(self, source, action)
        -- Always allow trusted sources
        if self.trustedSources[source] then
            return true
        end

        -- Check specific permission
        if self.permissions[action] == false then
            RUI.Logger:Warn("Permission denied: %s attempted %s", source or "unknown", action)
            return false
        end

        return true
    end,

    -- Sanitize user input
    SanitizeInput = function(self, input, inputType)
        if not input then return nil end

        if inputType == "string" then
            -- Remove potentially dangerous characters
            input = string.gsub(input, "[<>\"'&]", "")
            -- Limit length
            if string.len(input) > 1000 then
                input = string.sub(input, 1, 1000)
                RUI.Logger:Warn("Input truncated due to length")
            end

        elseif inputType == "number" then
            local num = tonumber(input)
            if not num then return nil end
            -- Clamp to reasonable range
            num = math.max(-999999, math.min(999999, num))
            return num

        elseif inputType == "frameName" then
            -- Only allow alphanumeric and underscore
            input = string.gsub(input, "[^%w_]", "")
            if string.len(input) > 50 then
                input = string.sub(input, 1, 50)
            end

        elseif inputType == "command" then
            -- Whitelist allowed commands
            local allowedCommands = {
                "show", "hide", "move", "scale", "reset", "config"
            }

            local isAllowed = false
            for _, cmd in pairs(allowedCommands) do
                if input == cmd then
                    isAllowed = true
                    break
                end
            end

            if not isAllowed then
                RUI.Logger:Warn("Blocked potentially dangerous command: %s", input)
                return nil
            end
        end

        return input
    end,

    -- Validate frame operations
    ValidateFrameOperation = function(self, frame, operation, source)
        if not frame then
            RUI.Logger:Error("Security: Invalid frame for operation %s", operation)
            return false
        end

        -- Check if frame is secure
        if frame.IsProtected and frame:IsProtected() then
            if not self:HasPermission(source, "modifySecureFrames") then
                RUI.Logger:Error("Security: Attempted to modify protected frame")
                return false
            end
        end

        -- Check operation type
        local dangerousOperations = {
            "SetScript", "RegisterEvent", "UnregisterEvent", "SetAttribute"
        }

        for _, dangerousOp in pairs(dangerousOperations) do
            if operation == dangerousOp and self.securityLevel >= 3 then
                if not self:HasPermission(source, "modifySecureFrames") then
                    RUI.Logger:Warn("Security: Blocked dangerous operation %s", operation)
                    return false
                end
            end
        end

        return true
    end,

    -- Secure data storage
    SecureStore = function(self, key, value, source)
        if not self:HasPermission(source, "saveSettings") then
            return false
        end

        -- Sanitize key
        key = self:SanitizeInput(key, "frameName")
        if not key then return false end

        -- Validate value
        if type(value) == "string" then
            value = self:SanitizeInput(value, "string")
        elseif type(value) == "number" then
            value = self:SanitizeInput(value, "number")
        elseif type(value) == "table" then
            -- Recursively sanitize table values
            value = self:SanitizeTable(value)
        end

        -- Store securely
        if not RUI.DB.profile.secureData then
            RUI.DB.profile.secureData = {}
        end

        RUI.DB.profile.secureData[key] = {
            value = value,
            source = source,
            timestamp = GetTime()
        }

        return true
    end,

    -- Sanitize table recursively
    SanitizeTable = function(self, tbl, depth)
        depth = depth or 0
        if depth > 10 then -- Prevent infinite recursion
            RUI.Logger:Warn("Security: Table depth limit reached")
            return {}
        end

        local sanitized = {}
        for k, v in pairs(tbl) do
            -- Sanitize key
            if type(k) == "string" then
                k = self:SanitizeInput(k, "string")
            end

            -- Sanitize value
            if type(v) == "string" then
                v = self:SanitizeInput(v, "string")
            elseif type(v) == "number" then
                v = self:SanitizeInput(v, "number")
            elseif type(v) == "table" then
                v = self:SanitizeTable(v, depth + 1)
            elseif type(v) == "function" then
                -- Never store functions
                RUI.Logger:Warn("Security: Blocked function storage")
                v = nil
            end

            if k and v ~= nil then
                sanitized[k] = v
            end
        end

        return sanitized
    end,

    -- Audit log for security events
    AuditLog = function(self, event, details, severity)
        severity = severity or "INFO"
        local timestamp = date("%Y-%m-%d %H:%M:%S")

        local logEntry = {
            timestamp = timestamp,
            event = event,
            details = details,
            severity = severity
        }

        -- Store in audit log
        if not RUI.DB.profile.auditLog then
            RUI.DB.profile.auditLog = {}
        end

        table.insert(RUI.DB.profile.auditLog, logEntry)

        -- Keep only last 100 entries
        if #RUI.DB.profile.auditLog > 100 then
            table.remove(RUI.DB.profile.auditLog, 1)
        end

        -- Log to console if high severity
        if severity == "WARN" or severity == "ERROR" then
            RUI.Logger[severity:lower()](RUI.Logger, "Security Audit: %s - %s", event, details)
        end
    end,

    -- Get security report
    GetSecurityReport = function(self)
        local report = {
            securityLevel = self.securityLevel,
            permissions = self.permissions,
            trustedSources = self.trustedSources,
            auditLogSize = 0,
            recentEvents = {}
        }

        if RUI.DB.profile.auditLog then
            report.auditLogSize = #RUI.DB.profile.auditLog
            -- Get last 10 events
            for i = math.max(1, #RUI.DB.profile.auditLog - 9), #RUI.DB.profile.auditLog do
                table.insert(report.recentEvents, RUI.DB.profile.auditLog[i])
            end
        end

        return report
    end,
}

-- Module management system
RUI.ModuleManager = {
    modules = {},
    loadOrder = {},

    -- Register a module
    RegisterModule = function(self, moduleName, moduleInstance)
        if self.modules[moduleName] then
            RUI.Logger:Warn("Module %s already registered, replacing", moduleName)
        end

        self.modules[moduleName] = moduleInstance
        table.insert(self.loadOrder, moduleName)

        RUI.Logger:Debug("Registered module: %s", moduleName)
    end,

    -- Get module safely
    GetModule = function(self, moduleName)
        return self.modules[moduleName]
    end,

    -- Initialize all modules in dependency order
    InitializeAllModules = function(self)
        RUI.Logger:Info("Initializing all modules...")

        local initialized = {}
        local failed = {}

        -- Sort modules by dependencies
        local sortedModules = self:SortModulesByDependencies()

        for _, moduleName in pairs(sortedModules) do
            local module = self.modules[moduleName]
            if module and module.Initialize then
                if module:Initialize() then
                    initialized[moduleName] = true
                    RUI.Logger:Debug("✅ Initialized: %s", moduleName)
                else
                    failed[moduleName] = true
                    RUI.Logger:Error("❌ Failed to initialize: %s", moduleName)
                end
            end
        end

        RUI.Logger:Info("Module initialization complete: %d success, %d failed",
            self:CountTable(initialized), self:CountTable(failed))
    end,

    -- Enable all modules
    EnableAllModules = function(self)
        RUI.Logger:Info("Enabling all modules...")

        local enabled = {}
        local failed = {}

        for _, moduleName in pairs(self.loadOrder) do
            local module = self.modules[moduleName]
            if module and module.Enable then
                if module:Enable() then
                    enabled[moduleName] = true
                else
                    failed[moduleName] = true
                end
            end
        end

        RUI.Logger:Info("Module enable complete: %d success, %d failed",
            self:CountTable(enabled), self:CountTable(failed))
    end,

    -- Disable all modules
    DisableAllModules = function(self)
        RUI.Logger:Info("Disabling all modules...")

        -- Disable in reverse order
        for i = #self.loadOrder, 1, -1 do
            local moduleName = self.loadOrder[i]
            local module = self.modules[moduleName]
            if module and module.Disable then
                module:Disable()
            end
        end
    end,

    -- Sort modules by dependencies
    SortModulesByDependencies = function(self)
        local sorted = {}
        local visited = {}
        local visiting = {}

        local function visit(moduleName)
            if visiting[moduleName] then
                RUI.Logger:Error("Circular dependency detected involving: %s", moduleName)
                return
            end

            if visited[moduleName] then
                return
            end

            visiting[moduleName] = true

            local module = self.modules[moduleName]
            if module and module.dependencies then
                for _, depName in pairs(module.dependencies) do
                    if self.modules[depName] then
                        visit(depName)
                    end
                end
            end

            visiting[moduleName] = nil
            visited[moduleName] = true
            table.insert(sorted, moduleName)
        end

        for moduleName, _ in pairs(self.modules) do
            visit(moduleName)
        end

        return sorted
    end,

    -- Get module status report
    GetStatusReport = function(self)
        local report = {
            totalModules = 0,
            initializedModules = 0,
            enabledModules = 0,
            failedModules = 0,
            modules = {}
        }

        for moduleName, module in pairs(self.modules) do
            report.totalModules = report.totalModules + 1

            local status = module:GetStatus()
            report.modules[moduleName] = status

            if status.initialized then
                report.initializedModules = report.initializedModules + 1
            end

            if status.enabled then
                report.enabledModules = report.enabledModules + 1
            end

            if status.hasErrors then
                report.failedModules = report.failedModules + 1
            end
        end

        return report
    end,

    -- Print status report
    PrintStatusReport = function(self)
        local report = self:GetStatusReport()

        RUI.Logger:Info("=== Module Status Report ===")
        RUI.Logger:Info("Total Modules: %d", report.totalModules)
        RUI.Logger:Info("Initialized: %d", report.initializedModules)
        RUI.Logger:Info("Enabled: %d", report.enabledModules)
        RUI.Logger:Info("Failed: %d", report.failedModules)

        RUI.Logger:Info("Module Details:")
        for moduleName, status in pairs(report.modules) do
            local statusIcon = "❌"
            if status.enabled then
                statusIcon = "✅"
            elseif status.initialized then
                statusIcon = "⚠️"
            end

            RUI.Logger:Info("  %s %s (v%s) - Frames: %d, Events: %d",
                statusIcon, moduleName, status.version, status.frameCount, status.eventCount)
        end
    end,

    -- Utility function to count table entries
    CountTable = function(self, tbl)
        local count = 0
        for _ in pairs(tbl) do
            count = count + 1
        end
        return count
    end,
}

-- Function to snap coordinates to grid (centered on screen)
local function SnapToGrid(x, y)
    local GRID_SIZE = RUI.DB.profile.gridSize or RUI.Constants.GRID.DEFAULT_SIZE
    local screenWidth = GetScreenWidth()
    local screenHeight = GetScreenHeight()
    local centerX = screenWidth / 2
    local centerY = screenHeight / 2

    -- Calculate offset from center to nearest grid point
    local offsetX = (x - centerX) % GRID_SIZE
    local offsetY = (y - centerY) % GRID_SIZE

    -- Snap to nearest grid point
    local snappedX = x - offsetX
    local snappedY = y - offsetY

    -- If closer to next grid point, snap to that instead
    if offsetX > GRID_SIZE/2 then
        snappedX = snappedX + GRID_SIZE
    end
    if offsetY > GRID_SIZE/2 then
        snappedY = snappedY + GRID_SIZE
    end

    return snappedX, snappedY
end

-- Table to store center dots for frames
local centerDots = {}

-- Function to create a center dot for a frame
local function CreateCenterDot(frame)
    if not frame or centerDots[frame] then return end

    local dot = frame:CreateTexture(nil, "OVERLAY")

    -- Use configurable size and color
    local dotSize = RUI.DB.profile.centerDotSize or RUI.Constants.CENTER_DOTS.DEFAULT_SIZE
    local dotColor = RUI.DB.profile.centerDotColor or RUI.Constants.CENTER_DOTS.DEFAULT_COLOR

    dot:SetSize(dotSize, dotSize)
    dot:SetPoint("CENTER", frame, "CENTER", 0, 0)
    dot:SetColorTexture(dotColor[1], dotColor[2], dotColor[3], dotColor[4])
    dot:SetDrawLayer("OVERLAY", RUI.Constants.CENTER_DOTS.DRAW_LAYER) -- High draw layer to ensure visibility

    centerDots[frame] = dot
    return dot
end

-- Function to remove center dot from a frame
local function RemoveCenterDot(frame)
    if not frame or not centerDots[frame] then return end

    centerDots[frame]:Hide()
    centerDots[frame] = nil
end

-- Function to show center dots for all frames
function RUI:ShowCenterDots()
    -- Check if center dots are enabled in settings
    if not self.DB.profile.showCenterDots then return end

    -- Get all modules that have frames using safe operations
    local moduleNames = {"UnitFrame", "CastingBar", "ActionBar", "Minimap", "QuestTracker", "BuffFrame"}

    for _, moduleName in pairs(moduleNames) do
        local module = self.SafeOps:GetModuleSafely(moduleName)
        if module and module.ShowEditorTest then
            -- Get frames from each module (this will need to be customized per module)
            if module.playerFrame then CreateCenterDot(module.playerFrame) end
            if module.targetFrame then CreateCenterDot(module.targetFrame) end
            if module.focusFrame then CreateCenterDot(module.focusFrame) end
            if module.petFrame then CreateCenterDot(module.petFrame) end
            if module.targetOfTargetFrame then CreateCenterDot(module.targetOfTargetFrame) end
            if module.minimapFrame then CreateCenterDot(module.minimapFrame) end
            if module.questTrackerFrame then CreateCenterDot(module.questTrackerFrame) end
            if module.questLogFrame then CreateCenterDot(module.questLogFrame) end
            if module.buffFrame then CreateCenterDot(module.buffFrame) end
            if module.playerCastingBar then CreateCenterDot(module.playerCastingBar) end
            if module.bossFrames then
                for _, bossFrame in pairs(module.bossFrames) do
                    CreateCenterDot(bossFrame)
                end
            end
        end
    end
end

-- Function to hide all center dots
function RUI:HideCenterDots()
    for frame, dot in pairs(centerDots) do
        if dot then
            dot:Hide()
        end
    end
    -- Clear the table
    centerDots = {}
end

-- Function to snap a frame to grid by its center point
local function SnapFrameToGridByCenter(frame)
    if not frame then return end

    -- Check if snapping is enabled
    if RUI.DB.profile.enableSnapping == false then return end

    -- Get frame's current center position
    local centerX, centerY = frame:GetCenter()
    if not centerX or not centerY then return end

    -- Check if center-based snapping is enabled
    if RUI.DB.profile.centerBasedSnapping == false then
        -- Use regular snapping instead (snap by anchor point)
        local point, relativeTo, relativePoint, xOfs, yOfs = frame:GetPoint()
        if point and xOfs and yOfs then
            local snappedX, snappedY = SnapToGrid(xOfs, yOfs)
            frame:ClearAllPoints()
            frame:SetPoint(point, relativeTo, relativePoint, snappedX, snappedY)
        end
        return
    end

    -- Snap the center position to grid
    local snappedCenterX, snappedCenterY = SnapToGrid(centerX, centerY)

    -- Calculate the offset needed to move the center to the snapped position
    local offsetX = snappedCenterX - centerX
    local offsetY = snappedCenterY - centerY

    -- Get current anchor point and position
    local point, relativeTo, relativePoint, xOfs, yOfs = frame:GetPoint()
    if point and xOfs and yOfs then
        -- Apply the offset to the current anchor position
        local newX = xOfs + offsetX
        local newY = yOfs + offsetY

        -- Reposition the frame
        frame:ClearAllPoints()
        frame:SetPoint(point, relativeTo, relativePoint, newX, newY)
    end
end

-- Function to check if editor mode is active
local function IsEditorModeActive()
    local editorModule = RUI:GetModule("EditorMode", true)
    return editorModule and editorModule:IsShown()
end

function RUI:OnInitialize()
	RUI.DB = AceDB:New("RetailUIDB", RUI.default, true)

	-- Register options tables
	AceConfig:RegisterOptionsTable("RetailUI", RUI.options)
	AceConfig:RegisterOptionsTable("RUI Commands", RUI.optionsSlash, "rui")

	-- Add to Blizzard Interface Options
	local AceConfigDialog = LibStub("AceConfigDialog-3.0")
	AceConfigDialog:AddToBlizOptions("RetailUI", "RetailUI")

	-- Initialize security system
	self.Security:Initialize()
end

function RUI:OnEnable()
    if GetCVar("useUiScale") == "0" then
        SetCVar("useUiScale", 1)
        SetCVar("uiScale", 0.75)
    end

    -- Initialize minimap button
    self:InitializeMinimapButton()

    -- Start periodic cleanup timer
    self:StartCleanupTimer()

    -- Register modules with module manager
    self:RegisterModules()
end

function RUI:OnDisable()
    -- Perform full cleanup on disable
    self.Memory:FullCleanup()

    -- Stop cleanup timer
    if self.cleanupTimer then
        self.cleanupTimer:SetScript("OnUpdate", nil)
        self.cleanupTimer = nil
    end
end

-- Start periodic cleanup timer
function RUI:StartCleanupTimer()
    if self.cleanupTimer then return end

    self.cleanupTimer = CreateFrame("Frame")
    local elapsed = 0
    local cleanupInterval = 300 -- 5 minutes
    local performanceCheckElapsed = 0
    local performanceCheckInterval = 600 -- 10 minutes

    self.cleanupTimer:SetScript("OnUpdate", function(self, dt)
        elapsed = elapsed + dt
        performanceCheckElapsed = performanceCheckElapsed + dt

        if elapsed >= cleanupInterval then
            elapsed = 0

            -- Perform periodic cleanup
            RUI.Memory:CleanupTimers()
            RUI.Throttle:Cleanup()

            -- Force garbage collection every 15 minutes
            if math.random(1, 3) == 1 then
                collectgarbage("collect")
                RUI.Logger:Debug("Performed periodic garbage collection")
            end
        end

        -- Performance monitoring
        if performanceCheckElapsed >= performanceCheckInterval then
            performanceCheckElapsed = 0

            -- Check system performance
            local metrics = RUI.Performance:GetSystemMetrics()

            -- Auto-warn about performance issues
            if metrics.frameRate < 20 then
                RUI.Logger:Warn("Critical: Frame rate dropped to %.1f FPS", metrics.frameRate)
                RUI.Security:AuditLog("PerformanceIssue", "Low FPS: " .. metrics.frameRate, "WARN")
            end

            if metrics.memoryUsage > 1024 * 1024 then -- 1 GB
                RUI.Logger:Warn("Critical: High memory usage: %.1f MB", metrics.memoryUsage / 1024)
                RUI.Security:AuditLog("PerformanceIssue", "High Memory: " .. (metrics.memoryUsage / 1024) .. "MB", "WARN")
            end

            -- Log performance metrics in debug mode
            if RUI.DB and RUI.DB.profile and RUI.DB.profile.debugMode then
                RUI.Logger:Debug("Performance Check - FPS: %.1f, Memory: %.1fMB, Latency: %dms",
                    metrics.frameRate, metrics.memoryUsage / 1024, metrics.latency)
            end
        end
    end)

    RUI.Logger:Debug("Started cleanup and performance monitoring timer")
end

-- Register all modules with the module manager
function RUI:RegisterModules()
    RUI.Logger:Info("Registering modules with module manager...")

    -- Get all loaded modules
    local moduleNames = {"UnitFrame", "ActionBar", "CastingBar", "Minimap", "BuffFrame", "QuestLog", "QuestTracker", "EditorMode"}

    for _, moduleName in pairs(moduleNames) do
        local module = self.SafeOps:GetModuleSafely(moduleName)
        if module then
            self.ModuleManager:RegisterModule(moduleName, module)
        else
            RUI.Logger:Warn("Module %s not found for registration", moduleName)
        end
    end

    -- Print module status
    self.ModuleManager:PrintStatusReport()
end

--- Creates a standardized UI frame with RetailUI styling and editor mode support
-- @param width number The width of the frame in pixels
-- @param height number The height of the frame in pixels
-- @param frameName string Unique identifier for the frame (alphanumeric and underscore only)
-- @return Frame|nil The created frame object, or nil if creation failed
-- @usage local frame = RUI:CreateUIFrame(200, 100, "MyFrame")
function RUI:CreateUIFrame(width, height, frameName)
	-- Validate inputs
	local validName, nameErr = self.Validator:ValidateFrameName(frameName)
	if not validName then
		self.Logger:Error("CreateUIFrame failed: %s", nameErr)
		return nil
	end

	local frame = CreateFrame("Frame", 'RUI_' .. frameName, UIParent)
	frame:SetSize(width, height)

	frame:RegisterForDrag("LeftButton")
	frame:EnableMouse(false)
	frame:SetMovable(false)
	frame:SetScript("OnDragStart", function(self, button)
		self:StartMoving()
	end)
	frame:SetScript("OnDragStop", function(self)
		self:StopMovingOrSizing()

		-- Apply center-based grid snapping if editor mode is active
		if IsEditorModeActive() then
			SnapFrameToGridByCenter(self)
		end
	end)

	frame:SetFrameLevel(RUI.Constants.FRAME_LEVELS.UI_FRAME)
	frame:SetFrameStrata(RUI.Constants.FRAME_STRATA.UI_FRAME)

	do
		local texture = frame:CreateTexture(nil, 'BACKGROUND')
		texture:SetAllPoints(frame)
		texture:SetTexture(RUI.Constants.TEXTURES.ACTION_BAR_HORIZONTAL)
		local coords = RUI.Constants.TEXTURE_COORDS.ACTION_BAR_HORIZONTAL
		texture:SetTexCoord(coords[1], coords[2], coords[3], coords[4])
		texture:Hide()

		frame.editorTexture = texture
	end

	do
		local fontString = frame:CreateFontString(nil, "BORDER", 'GameFontNormal')
		fontString:SetAllPoints(frame)
		fontString:SetText(frameName)
		fontString:Hide()

		frame.editorText = fontString
	end

	-- Register frame for memory management
	self.Memory:RegisterFrame(frame, "Core", frameName)

	-- Add accessibility support
	if self.Accessibility then
		self.Accessibility:AddAccessibilityLabel(frame, frameName, "RetailUI " .. frameName .. " frame")
	end

	return frame
end

-- Frame management for editor mode
RUI.editorFrames = {}

function RUI:ShowUIFrame(frame)
	if not self.Validator:ValidateFrame(frame) then
		self.Logger:Error("ShowUIFrame: Invalid frame provided")
		return
	end

	frame:SetMovable(false)
	frame:EnableMouse(false)

	if frame.editorTexture then frame.editorTexture:Hide() end
	if frame.editorText then frame.editorText:Hide() end

	if self.editorFrames[frame] then
		for _, target in pairs(self.editorFrames[frame]) do
			if target and target.SetAlpha then
				target:SetAlpha(1)
			end
		end
		self.editorFrames[frame] = nil
	end
end

function RUI:HideUIFrame(frame, exclude)
	if not self.Validator:ValidateFrame(frame) then
		self.Logger:Error("HideUIFrame: Invalid frame provided")
		return
	end

	frame:SetMovable(true)
	frame:EnableMouse(true)

	if frame.editorTexture then frame.editorTexture:Show() end
	if frame.editorText then frame.editorText:Show() end

	self.editorFrames[frame] = {}
	exclude = exclude or {}

	for _, target in pairs(exclude) do
		if target and target.SetAlpha then
			target:SetAlpha(0)
			tinsert(self.editorFrames[frame], target)
		end
	end
end

--- Saves the current position of a UI frame with security validation and grid snapping
-- @param frame Frame The frame object to save position for
-- @param widgetName string Widget identifier for storage (will be sanitized)
-- @return boolean True if position was saved successfully
-- @usage RUI:SaveUIFramePosition(myFrame, "myWidget")
function RUI:SaveUIFramePosition(frame, widgetName)
	-- Security check
	if not self.Security:HasPermission("RetailUI", "saveSettings") then
		return false
	end

	if not self.Security:ValidateFrameOperation(frame, "SavePosition", "RetailUI") then
		return false
	end

	-- Validate inputs
	local validFrame, frameErr = self.Validator:ValidateFrame(frame)
	if not validFrame then
		self.Logger:Error("SaveUIFramePosition failed: %s", frameErr)
		return
	end

	-- Sanitize widget name
	widgetName = self.Security:SanitizeInput(widgetName, "frameName")
	if not widgetName then
		self.Logger:Error("SaveUIFramePosition failed: Invalid widget name")
		return
	end

	-- Get the current anchor point (not center) for consistent saving
	local point, _, relativePoint, posX, posY = frame:GetPoint()

	-- Apply center-based grid snapping when saving position
	if posX and posY then
		-- First snap the frame by center to ensure it's properly aligned
		SnapFrameToGridByCenter(frame)
		-- Then get the updated position after snapping
		point, _, relativePoint, posX, posY = frame:GetPoint()
	end

	-- Ensure widgets table exists
	if not self.DB.profile.widgets[widgetName] then
		self.DB.profile.widgets[widgetName] = {}
	end

	-- Use secure storage
	local positionData = {
		anchor = relativePoint or point,
		posX = posX,
		posY = posY
	}

	if self.Security:SecureStore(widgetName .. "_position", positionData, "RetailUI") then
		-- Also store in regular location for compatibility
		self.DB.profile.widgets[widgetName].anchor = relativePoint or point
		self.DB.profile.widgets[widgetName].posX = posX
		self.DB.profile.widgets[widgetName].posY = posY

		self.Logger:Debug("Saved position for %s: %s %.0f,%.0f", widgetName, relativePoint or point, posX or 0, posY or 0)
		self.Security:AuditLog("FramePositionSaved", "Widget: " .. widgetName, "INFO")
	else
		self.Logger:Error("Failed to securely store position for %s", widgetName)
	end
end

--- Saves the scale of a UI frame with input validation and security checks
-- @param input string|number Scale value (will be validated to 0.1-5.0 range)
-- @param widgetName string Widget identifier for storage (will be sanitized)
-- @return boolean True if scale was saved successfully
-- @usage RUI:SaveUIFrameScale("1.5", "myWidget")
function RUI:SaveUIFrameScale(input, widgetName)
	-- Security check
	if not self.Security:HasPermission("RetailUI", "saveSettings") then
		return false
	end

	-- Sanitize inputs
	input = self.Security:SanitizeInput(input, "number")
	widgetName = self.Security:SanitizeInput(widgetName, "frameName")

	if not input or not widgetName then
		self.Logger:Error("SaveUIFrameScale failed: Invalid input after sanitization")
		return
	end

	-- Validate inputs using our validator
	local validScale, scaleResult = self.Validator:ValidateScale(input)
	if not validScale then
		self.Logger:Error("SaveUIFrameScale failed: %s", scaleResult)
		return
	end

	-- Ensure widgets table exists
	if not self.DB.profile.widgets[widgetName] then
		self.DB.profile.widgets[widgetName] = {}
	end

	-- Use secure storage
	if self.Security:SecureStore(widgetName .. "_scale", scaleResult, "RetailUI") then
		-- Also store in regular location for compatibility
		self.DB.profile.widgets[widgetName].scale = scaleResult

		-- Update the UI to reflect the changes
		local UnitFrameModule = self.SafeOps:GetModuleSafely("UnitFrame")
		if UnitFrameModule and UnitFrameModule.UpdateWidgets then
			UnitFrameModule:UpdateWidgets()
		end

		self.Logger:Info("%s Frame Scale saved as %.2f", widgetName, scaleResult)
		self.Security:AuditLog("FrameScaleSaved", "Widget: " .. widgetName .. ", Scale: " .. scaleResult, "INFO")
	else
		self.Logger:Error("Failed to securely store scale for %s", widgetName)
	end
end

--- Retrieves the saved scale for a widget
-- @param widgetName string Widget identifier to retrieve scale for
-- @return number Scale value (default: 1.0 if not found or invalid)
-- @usage local scale = RUI:GetUIFrameScale("myWidget")
function RUI:GetUIFrameScale(widgetName)
	local validName, nameErr = self.Validator:ValidateFrameName(widgetName)
	if not validName then
		self.Logger:Error("GetUIFrameScale failed: %s", nameErr)
		return 1.0 -- Default scale
	end

	if self.DB.profile.widgets[widgetName] and self.DB.profile.widgets[widgetName].scale then
		return self.DB.profile.widgets[widgetName].scale
	end
	return 1.0 -- Default scale
end

function RUI:CheckSettingsExists(module, widgets)
	if not module then
		self.Logger:Error("CheckSettingsExists: No module provided")
		return
	end

	for _, widget in pairs(widgets) do
		if self.DB.profile.widgets[widget] == nil then
			if module.LoadDefaultSettings then
				module:LoadDefaultSettings()
			else
				self.Logger:Warn("Module %s missing LoadDefaultSettings method", module.moduleName or "unknown")
			end
			break
		end
	end

	if module.UpdateWidgets then
		module:UpdateWidgets()
	else
		self.Logger:Warn("Module %s missing UpdateWidgets method", module.moduleName or "unknown")
	end
end

local function MoveChatOnFirstLoad()
    local chat = ChatFrame1
    if not chat then return end

    if chat:IsUserPlaced() then return end

    chat:ClearAllPoints()
    chat:SetPoint("BOTTOMLEFT", UIParent, "BOTTOMLEFT", 32, 32)
    chat:SetWidth(chat:GetWidth() - 40)
    chat:SetMovable(true)
    chat:SetUserPlaced(true)
end

local f = CreateFrame("Frame")
f:RegisterEvent("PLAYER_ENTERING_WORLD")
f:SetScript("OnEvent", function(self, event)
    MoveChatOnFirstLoad()
    self:UnregisterEvent("PLAYER_ENTERING_WORLD")
end)
