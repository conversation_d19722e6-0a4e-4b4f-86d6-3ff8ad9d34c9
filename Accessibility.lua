--[[
    Copyright (c) Dmitriy. All rights reserved.
    Licensed under the MIT license. See LICENSE file in the project root for details.
]]

-- Accessibility and inclusivity system for RetailUI
-- Provides support for users with disabilities and diverse needs

RUI.Accessibility = {
    -- Accessibility settings
    settings = {
        screenReaderSupport = false,
        highContrast = false,
        largeText = false,
        keyboardNavigation = true,
        colorBlindSupport = false,
        reducedMotion = false,
        audioFeedback = false,
        tooltipVerbosity = "normal", -- "minimal", "normal", "verbose"
    },
    
    -- Color schemes for accessibility
    colorSchemes = {
        default = {
            background = {0.1, 0.1, 0.1, 0.8},
            text = {1, 1, 1, 1},
            accent = {0.2, 0.6, 1, 1},
            warning = {1, 0.8, 0, 1},
            error = {1, 0.2, 0.2, 1}
        },
        highContrast = {
            background = {0, 0, 0, 1},
            text = {1, 1, 1, 1},
            accent = {1, 1, 0, 1},
            warning = {1, 1, 0, 1},
            error = {1, 0, 0, 1}
        },
        colorBlind = {
            background = {0.1, 0.1, 0.1, 0.8},
            text = {1, 1, 1, 1},
            accent = {0, 0.7, 1, 1}, -- Blue instead of green
            warning = {1, 0.6, 0, 1}, -- Orange instead of yellow
            error = {0.8, 0, 0.8, 1} -- Magenta instead of red
        }
    },
    
    -- Initialize accessibility system
    Initialize = function(self)
        -- Load accessibility settings
        self:LoadSettings()
        
        -- Apply initial accessibility features
        self:ApplyAccessibilitySettings()
        
        -- Register keyboard handlers
        self:SetupKeyboardNavigation()
        
        -- Setup screen reader support
        if self.settings.screenReaderSupport then
            self:EnableScreenReaderSupport()
        end
        
        RUI.Logger:Info("Accessibility system initialized")
    end,
    
    -- Load accessibility settings
    LoadSettings = function(self)
        if RUI.DB and RUI.DB.profile and RUI.DB.profile.accessibility then
            for key, value in pairs(RUI.DB.profile.accessibility) do
                if self.settings[key] ~= nil then
                    self.settings[key] = value
                end
            end
        end
    end,
    
    -- Save accessibility settings
    SaveSettings = function(self)
        if not RUI.DB.profile.accessibility then
            RUI.DB.profile.accessibility = {}
        end
        
        for key, value in pairs(self.settings) do
            RUI.DB.profile.accessibility[key] = value
        end
        
        RUI.Logger:Info("Accessibility settings saved")
    end,
    
    -- Apply accessibility settings
    ApplyAccessibilitySettings = function(self)
        -- Apply color scheme
        local scheme = "default"
        if self.settings.highContrast then
            scheme = "highContrast"
        elseif self.settings.colorBlindSupport then
            scheme = "colorBlind"
        end
        
        self:ApplyColorScheme(scheme)
        
        -- Apply text scaling
        if self.settings.largeText then
            self:ApplyTextScaling(1.25)
        else
            self:ApplyTextScaling(1.0)
        end
        
        -- Apply motion settings
        if self.settings.reducedMotion then
            self:ReduceAnimations()
        end
        
        RUI.Logger:Debug("Applied accessibility settings")
    end,
    
    -- Apply color scheme
    ApplyColorScheme = function(self, schemeName)
        local scheme = self.colorSchemes[schemeName] or self.colorSchemes.default
        
        -- Store current scheme for reference
        self.currentColorScheme = scheme
        
        -- Apply to existing UI elements
        self:UpdateUIColors(scheme)
        
        RUI.Logger:Debug("Applied color scheme: %s", schemeName)
    end,
    
    -- Update UI colors
    UpdateUIColors = function(self, scheme)
        -- This would update colors of existing frames
        -- For now, we'll store the scheme for new frames to use
        RUI.Constants.COLORS.ACCESSIBILITY = scheme
    end,
    
    -- Apply text scaling
    ApplyTextScaling = function(self, scale)
        self.textScale = scale
        
        -- Update font sizes for existing elements
        -- This would typically iterate through all text elements
        RUI.Logger:Debug("Applied text scaling: %.2f", scale)
    end,
    
    -- Reduce animations for motion sensitivity
    ReduceAnimations = function(self)
        -- Disable or reduce animations
        self.animationsReduced = true
        RUI.Logger:Debug("Reduced animations for motion sensitivity")
    end,
    
    -- Setup keyboard navigation
    SetupKeyboardNavigation = function(self)
        if not self.settings.keyboardNavigation then return end
        
        -- Create keyboard navigation handler
        self.keyboardFrame = CreateFrame("Frame")
        self.keyboardFrame:SetScript("OnKeyDown", function(self, key)
            RUI.Accessibility:HandleKeyboardInput(key)
        end)
        
        -- Enable keyboard input
        self.keyboardFrame:EnableKeyboard(true)
        self.keyboardFrame:SetPropagateKeyboardInput(true)
        
        RUI.Logger:Debug("Keyboard navigation enabled")
    end,
    
    -- Handle keyboard input
    HandleKeyboardInput = function(self, key)
        if not self.settings.keyboardNavigation then return end
        
        -- Handle accessibility shortcuts
        if IsControlKeyDown() and IsAltKeyDown() then
            if key == "H" then
                self:ToggleHighContrast()
            elseif key == "T" then
                self:ToggleLargeText()
            elseif key == "S" then
                self:ToggleScreenReader()
            elseif key == "A" then
                self:ShowAccessibilityMenu()
            end
        end
    end,
    
    -- Enable screen reader support
    EnableScreenReaderSupport = function(self)
        self.screenReaderEnabled = true
        
        -- Create screen reader announcements
        self.announceFrame = CreateFrame("Frame")
        
        -- Hook into UI events for announcements
        self:HookUIEvents()
        
        RUI.Logger:Info("Screen reader support enabled")
    end,
    
    -- Hook UI events for screen reader
    HookUIEvents = function(self)
        -- Hook frame show/hide events
        local originalShow = getmetatable(CreateFrame("Frame")).__index.Show
        getmetatable(CreateFrame("Frame")).__index.Show = function(frame)
            originalShow(frame)
            if self.screenReaderEnabled and frame.accessibilityLabel then
                self:AnnounceToScreenReader(frame.accessibilityLabel .. " shown")
            end
        end
        
        local originalHide = getmetatable(CreateFrame("Frame")).__index.Hide
        getmetatable(CreateFrame("Frame")).__index.Hide = function(frame)
            originalHide(frame)
            if self.screenReaderEnabled and frame.accessibilityLabel then
                self:AnnounceToScreenReader(frame.accessibilityLabel .. " hidden")
            end
        end
    end,
    
    -- Announce to screen reader
    AnnounceToScreenReader = function(self, text)
        if not self.screenReaderEnabled then return end
        
        -- Create temporary announcement frame
        local announceFrame = CreateFrame("Frame", nil, UIParent)
        local announceText = announceFrame:CreateFontString(nil, "OVERLAY", "GameFontNormal")
        announceText:SetText(text)
        announceText:SetPoint("CENTER", UIParent, "CENTER", -5000, -5000) -- Off-screen
        
        -- Remove after brief delay
        RUI.Compat.After(0.1, function()
            if announceFrame then
                announceFrame:Hide()
            end
        end)
        
        RUI.Logger:Debug("Screen reader announcement: %s", text)
    end,
    
    -- Add accessibility label to frame
    AddAccessibilityLabel = function(self, frame, label, description)
        if not frame then return end
        
        frame.accessibilityLabel = label
        frame.accessibilityDescription = description or label
        
        -- Add tooltip with accessibility info
        if self.settings.tooltipVerbosity ~= "minimal" then
            frame:SetScript("OnEnter", function(self)
                GameTooltip:SetOwner(self, "ANCHOR_RIGHT")
                GameTooltip:SetText(frame.accessibilityLabel)
                
                if RUI.Accessibility.settings.tooltipVerbosity == "verbose" and frame.accessibilityDescription then
                    GameTooltip:AddLine(frame.accessibilityDescription, 1, 1, 1, true)
                end
                
                GameTooltip:Show()
            end)
            
            frame:SetScript("OnLeave", function(self)
                GameTooltip:Hide()
            end)
        end
    end,
    
    -- Toggle high contrast mode
    ToggleHighContrast = function(self)
        self.settings.highContrast = not self.settings.highContrast
        self:ApplyAccessibilitySettings()
        self:SaveSettings()
        
        local status = self.settings.highContrast and "enabled" or "disabled"
        RUI.Logger:Info("High contrast mode %s", status)
        
        if self.screenReaderEnabled then
            self:AnnounceToScreenReader("High contrast mode " .. status)
        end
    end,
    
    -- Toggle large text mode
    ToggleLargeText = function(self)
        self.settings.largeText = not self.settings.largeText
        self:ApplyAccessibilitySettings()
        self:SaveSettings()
        
        local status = self.settings.largeText and "enabled" or "disabled"
        RUI.Logger:Info("Large text mode %s", status)
        
        if self.screenReaderEnabled then
            self:AnnounceToScreenReader("Large text mode " .. status)
        end
    end,
    
    -- Toggle screen reader support
    ToggleScreenReader = function(self)
        self.settings.screenReaderSupport = not self.settings.screenReaderSupport
        
        if self.settings.screenReaderSupport then
            self:EnableScreenReaderSupport()
        else
            self.screenReaderEnabled = false
        end
        
        self:SaveSettings()
        
        local status = self.settings.screenReaderSupport and "enabled" or "disabled"
        RUI.Logger:Info("Screen reader support %s", status)
    end,
    
    -- Show accessibility menu
    ShowAccessibilityMenu = function(self)
        RUI.Logger:Info("=== Accessibility Options ===")
        RUI.Logger:Info("Ctrl+Alt+H - Toggle High Contrast")
        RUI.Logger:Info("Ctrl+Alt+T - Toggle Large Text")
        RUI.Logger:Info("Ctrl+Alt+S - Toggle Screen Reader")
        RUI.Logger:Info("Ctrl+Alt+A - Show This Menu")
        RUI.Logger:Info("Current Settings:")
        RUI.Logger:Info("  High Contrast: %s", self.settings.highContrast and "ON" or "OFF")
        RUI.Logger:Info("  Large Text: %s", self.settings.largeText and "ON" or "OFF")
        RUI.Logger:Info("  Screen Reader: %s", self.settings.screenReaderSupport and "ON" or "OFF")
        RUI.Logger:Info("  Keyboard Navigation: %s", self.settings.keyboardNavigation and "ON" or "OFF")
        
        if self.screenReaderEnabled then
            self:AnnounceToScreenReader("Accessibility menu displayed in chat")
        end
    end,
    
    -- Get accessibility status
    GetAccessibilityStatus = function(self)
        return {
            screenReaderSupport = self.settings.screenReaderSupport,
            highContrast = self.settings.highContrast,
            largeText = self.settings.largeText,
            keyboardNavigation = self.settings.keyboardNavigation,
            colorBlindSupport = self.settings.colorBlindSupport,
            reducedMotion = self.settings.reducedMotion,
            audioFeedback = self.settings.audioFeedback,
            currentColorScheme = self.currentColorScheme,
            textScale = self.textScale or 1.0
        }
    end,
    
    -- Create accessible frame
    CreateAccessibleFrame = function(self, frameType, name, parent, template, label, description)
        local frame = CreateFrame(frameType, name, parent, template)
        
        if frame and label then
            self:AddAccessibilityLabel(frame, label, description)
            
            -- Apply current accessibility settings
            if self.currentColorScheme then
                -- Apply color scheme to frame
                if frame.SetBackdropColor then
                    local bg = self.currentColorScheme.background
                    frame:SetBackdropColor(bg[1], bg[2], bg[3], bg[4])
                end
            end
        end
        
        return frame
    end,
}

-- Initialize accessibility system
local function InitializeAccessibility()
    if RUI and RUI.Accessibility then
        RUI.Accessibility:Initialize()
    end
end

-- Register for delayed initialization
local accessFrame = CreateFrame("Frame")
accessFrame:RegisterEvent("ADDON_LOADED")
accessFrame:SetScript("OnEvent", function(self, event, addonName)
    if addonName == "RetailUI" then
        -- Delay initialization to ensure all systems are ready
        RUI.Compat.After(1.0, InitializeAccessibility)
        self:UnregisterEvent("ADDON_LOADED")
    end
end)
